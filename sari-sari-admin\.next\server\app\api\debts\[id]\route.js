(()=>{var e={};e.id=874,e.ids=[874],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>x,serverHooks:()=>q,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>c,GET:()=>p,PUT:()=>d});var o=r(96559),i=r(48088),a=r(37719),u=r(32190),n=r(56621);async function p(e,{params:t}){try{let{id:e}=await t,{data:r,error:s}=await n.N.from("customer_debts").select("*").eq("id",e).single();if(s)return u.NextResponse.json({error:s.message},{status:500});if(!r)return u.NextResponse.json({error:"Debt record not found"},{status:404});return u.NextResponse.json({debt:r})}catch{return u.NextResponse.json({error:"Failed to fetch debt record"},{status:500})}}async function d(e,{params:t}){try{let{id:r}=await t,{customer_name:s,customer_family_name:o,product_name:i,product_price:a,quantity:p,debt_date:d}=await e.json();if(!s||!o||!i||!a||!p)return u.NextResponse.json({error:"Missing required fields"},{status:400});let{data:c,error:x}=await n.N.from("customer_debts").update({customer_name:s,customer_family_name:o,product_name:i,product_price:parseFloat(a),quantity:parseInt(p),debt_date:d||new Date().toISOString().split("T")[0]}).eq("id",r).select().single();if(x)return u.NextResponse.json({error:x.message},{status:500});return u.NextResponse.json({debt:c})}catch{return u.NextResponse.json({error:"Failed to update debt record"},{status:500})}}async function c(e,{params:t}){try{let{id:e}=await t,{error:r}=await n.N.from("customer_debts").delete().eq("id",e);if(r)return u.NextResponse.json({error:r.message},{status:500});return u.NextResponse.json({message:"Debt record deleted successfully"})}catch{return u.NextResponse.json({error:"Failed to delete debt record"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/debts/[id]/route",pathname:"/api/debts/[id]",filename:"route",bundlePath:"app/api/debts/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\debts\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:f,serverHooks:q}=x;function m(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:f})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});let s=(0,r(66437).UU)("your_supabase_project_url","your_supabase_anon_key")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,437],()=>r(28204));module.exports=s})();