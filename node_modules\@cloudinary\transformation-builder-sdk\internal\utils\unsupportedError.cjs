'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib_es6 = require('../../tslib.es6-7a681263.cjs');

var UnsupportedError = /** @class */ (function (_super) {
    tslib_es6.__extends(UnsupportedError, _super);
    function UnsupportedError(message) {
        if (message === void 0) { message = 'Unsupported'; }
        return _super.call(this, message) || this;
    }
    return UnsupportedError;
}(Error));
/**
 * Creates a new UnsupportedError
 * @param message
 */
function createUnsupportedError(message) {
    return new UnsupportedError(message);
}

exports.UnsupportedError = UnsupportedError;
exports.createUnsupportedError = createUnsupportedError;
