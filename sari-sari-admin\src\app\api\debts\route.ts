import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// GET - Fetch all customer debts
export async function GET() {
  try {
    const { data: debts, error } = await supabase
      .from('customer_debts')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ debts })
  } catch {
    return NextResponse.json(
      { error: 'Failed to fetch customer debts' },
      { status: 500 }
    )
  }
}

// POST - Create new customer debt
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      product_name,
      product_price,
      quantity,
      debt_date,
    } = body

    // Validate required fields
    if (
      !customer_name ||
      !customer_family_name ||
      !product_name ||
      !product_price ||
      !quantity
    ) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const { data: debt, error } = await supabase
      .from('customer_debts')
      .insert([
        {
          customer_name,
          customer_family_name,
          product_name,
          product_price: parseFloat(product_price),
          quantity: parseInt(quantity),
          debt_date: debt_date || new Date().toISOString().split('T')[0],
        },
      ])
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ debt }, { status: 201 })
  } catch {
    return NextResponse.json(
      { error: 'Failed to create customer debt' },
      { status: 500 }
    )
  }
}
