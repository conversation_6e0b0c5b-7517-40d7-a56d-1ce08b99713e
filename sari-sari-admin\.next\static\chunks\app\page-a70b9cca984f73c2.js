(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2202:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(5155),a=s(2115),l=s(3783),c=s(7108),n=s(7580),o=s(5937);function d(e){let{activeSection:t,setActiveSection:s}=e,a=[{id:"dashboard",label:"Dashboard",icon:l.A},{id:"products",label:"Product Lists",icon:c.A},{id:"debts",label:"Customer Debts",icon:n.A}];return(0,r.jsxs)("div",{className:"w-64 bg-white shadow-lg",children:[(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Sari-Sari Admin"})]})}),(0,r.jsx)("nav",{className:"mt-6",children:(0,r.jsx)("div",{className:"px-3",children:a.map(e=>{let a=e.icon,l=t===e.id;return(0,r.jsxs)("button",{onClick:()=>s(e.id),className:"w-full flex items-center px-3 py-2 mb-1 text-left rounded-lg transition-colors ".concat(l?"bg-blue-100 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),children:[(0,r.jsx)(a,{className:"h-5 w-5 mr-3"}),e.label]},e.id)})})}),(0,r.jsx)("div",{className:"absolute bottom-0 w-64 p-6 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[(0,r.jsx)("p",{children:"Sari-Sari Store"}),(0,r.jsx)("p",{children:"Admin Dashboard v1.0"})]})})]})}var i=s(7924),u=s(4616),m=s(3717),x=s(2525),b=s(4416),h=s(9869);(0,s(5647).UU)("your_supabase_project_url","your_supabase_anon_key");let g=["Snacks","Canned Goods","Beverages","Personal Care","Household Items","Condiments","Rice & Grains","Instant Foods","Dairy Products","Others"];function p(e){let{isOpen:t,onClose:s,product:l}=e,[n,o]=(0,a.useState)({name:"",net_weight:"",price:"",stock_quantity:"",category:"",image_url:""}),[d,i]=(0,a.useState)(null),[u,m]=(0,a.useState)(""),[x,p]=(0,a.useState)(!1),[y,f]=(0,a.useState)(!1);(0,a.useEffect)(()=>{l?(o({name:l.name,net_weight:l.net_weight,price:l.price.toString(),stock_quantity:l.stock_quantity.toString(),category:l.category,image_url:l.image_url||""}),m(l.image_url||"")):(o({name:"",net_weight:"",price:"",stock_quantity:"",category:"",image_url:""}),m("")),i(null)},[l,t]);let j=async()=>{if(!d)return n.image_url;f(!0);try{let e=new FormData;e.append("file",d);let t=await fetch("/api/upload",{method:"POST",body:e});return(await t.json()).url}catch(e){return console.error("Error uploading image:",e),n.image_url}finally{f(!1)}},N=async e=>{e.preventDefault(),p(!0);try{let e=await j(),t={...n,image_url:e,price:parseFloat(n.price),stock_quantity:parseInt(n.stock_quantity)},r=l?"/api/products/".concat(l.id):"/api/products",a=l?"PUT":"POST";(await fetch(r,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok?s():console.error("Error saving product")}catch(e){console.error("Error saving product:",e)}finally{p(!1)}};return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:l?"Edit Product in List":"Add Product to List"}),(0,r.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(b.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Product Image"}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-2",children:u?(0,r.jsx)("img",{src:u,alt:"Preview",className:"w-full h-full object-cover rounded-lg"}):(0,r.jsx)(c.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];if(s){i(s);let e=new FileReader;e.onloadend=()=>{m(e.result)},e.readAsDataURL(s)}},className:"hidden",id:"image-upload"}),(0,r.jsxs)("label",{htmlFor:"image-upload",className:"flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Choose Image"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:n.name,onChange:e=>o({...n,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Net Weight *"}),(0,r.jsx)("input",{type:"text",required:!0,placeholder:"e.g., 100g, 1L, 250ml",value:n.net_weight,onChange:e=>o({...n,net_weight:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price (₱) *"}),(0,r.jsx)("input",{type:"number",step:"0.01",min:"0",required:!0,value:n.price,onChange:e=>o({...n,price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Stock Quantity *"}),(0,r.jsx)("input",{type:"number",min:"0",required:!0,value:n.stock_quantity,onChange:e=>o({...n,stock_quantity:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category *"}),(0,r.jsxs)("select",{required:!0,value:n.category,onChange:e=>o({...n,category:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"Select Category"}),g.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:s,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:x||y,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:x||y?"Saving...":l?"Update in List":"Add to List"})]})]})]})}):null}function y(e){let{onStatsUpdate:t}=e,[s,l]=(0,a.useState)([]),[n,o]=(0,a.useState)(!0),[d,b]=(0,a.useState)(""),[h,y]=(0,a.useState)(""),[f,j]=(0,a.useState)(!1),[N,v]=(0,a.useState)(null);(0,a.useEffect)(()=>{w()},[]);let w=async()=>{try{let e=await fetch("/api/products"),t=await e.json();l(t.products||[])}catch(e){console.error("Error fetching products:",e)}finally{o(!1)}},_=async e=>{if(confirm("Are you sure you want to delete this product?"))try{(await fetch("/api/products/".concat(e),{method:"DELETE"})).ok&&(l(s.filter(t=>t.id!==e)),t())}catch(e){console.error("Error deleting product:",e)}},C=e=>{v(e),j(!0)},k=s.filter(e=>{let t=e.name.toLowerCase().includes(d.toLowerCase()),s=""===h||e.category===h;return t&&s});return n?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search products...",value:d,onChange:e=>b(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("select",{value:h,onChange:e=>y(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"All Categories"}),g.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)("button",{onClick:()=>j(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Add to Product List"]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:k.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 flex items-center justify-center",children:e.image_url?(0,r.jsx)("img",{src:e.image_url,alt:e.name,className:"w-full h-full object-cover"}):(0,r.jsx)(c.A,{className:"h-16 w-16 text-gray-400"})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.category}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",e.price]}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:e.net_weight})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("span",{className:"text-sm ".concat(e.stock_quantity<10?"text-red-600":"text-gray-600"),children:["Stock: ",e.stock_quantity]}),e.stock_quantity<10&&(0,r.jsx)("span",{className:"text-xs bg-red-100 text-red-800 px-2 py-1 rounded",children:"Low Stock"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>C(e),className:"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,r.jsxs)("button",{onClick:()=>_(e.id),className:"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"Delete"]})]})]})]},e.id))}),0===k.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(c.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products in list"}),(0,r.jsx)("p",{className:"text-gray-600",children:d||h?"Try adjusting your search or filter criteria":"Get started by adding your first product to the list"})]}),(0,r.jsx)(p,{isOpen:f,onClose:()=>{j(!1),v(null),w(),t()},product:N})]})}var f=s(9074);function j(e){let{isOpen:t,onClose:s,debt:l}=e,[c,n]=(0,a.useState)({customer_name:"",customer_family_name:"",product_name:"",product_price:"",quantity:"",debt_date:""}),[o,d]=(0,a.useState)(!1);(0,a.useEffect)(()=>{l?n({customer_name:l.customer_name,customer_family_name:l.customer_family_name,product_name:l.product_name,product_price:l.product_price.toString(),quantity:l.quantity.toString(),debt_date:l.debt_date}):n({customer_name:"",customer_family_name:"",product_name:"",product_price:"",quantity:"",debt_date:new Date().toISOString().split("T")[0]})},[l,t]);let i=async e=>{e.preventDefault(),d(!0);try{let e={...c,product_price:parseFloat(c.product_price),quantity:parseInt(c.quantity)},t=l?"/api/debts/".concat(l.id):"/api/debts",r=l?"PUT":"POST";(await fetch(t,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?s():console.error("Error saving debt record")}catch(e){console.error("Error saving debt record:",e)}finally{d(!1)}};if(!t)return null;let u=c.product_price&&c.quantity?(parseFloat(c.product_price)*parseInt(c.quantity)).toFixed(2):"0.00";return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:l?"Edit Debt Record":"Add New Debt Record"}),(0,r.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(b.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("form",{onSubmit:i,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Customer First Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:c.customer_name,onChange:e=>n({...c,customer_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Juan"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Customer Family Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:c.customer_family_name,onChange:e=>n({...c,customer_family_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Dela Cruz"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:c.product_name,onChange:e=>n({...c,product_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Lucky Me Pancit Canton"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Price (₱) *"}),(0,r.jsx)("input",{type:"number",step:"0.01",min:"0",required:!0,value:c.product_price,onChange:e=>n({...c,product_price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"0.00"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Quantity *"}),(0,r.jsx)("input",{type:"number",min:"1",required:!0,value:c.quantity,onChange:e=>n({...c,quantity:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"1"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Debt Date *"}),(0,r.jsx)("input",{type:"date",required:!0,value:c.debt_date,onChange:e=>n({...c,debt_date:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsx)("div",{className:"bg-gray-50 p-3 rounded-md",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Total Amount:"}),(0,r.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",u]})]})}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:s,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:o,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:o?"Saving...":l?"Update":"Add Record"})]})]})]})})}var N=s(3319);function v(e){let{onStatsUpdate:t}=e,[s,l]=(0,a.useState)([]),[c,o]=(0,a.useState)(!0),[d,b]=(0,a.useState)(""),[h,g]=(0,a.useState)(!1),[p,y]=(0,a.useState)(null);(0,a.useEffect)(()=>{v()},[]);let v=async()=>{try{let e=await fetch("/api/debts"),t=await e.json();l(t.debts||[])}catch(e){console.error("Error fetching debts:",e)}finally{o(!1)}},w=async e=>{if(confirm("Are you sure you want to delete this debt record?"))try{(await fetch("/api/debts/".concat(e),{method:"DELETE"})).ok&&(l(s.filter(t=>t.id!==e)),t())}catch(e){console.error("Error deleting debt:",e)}},_=e=>{y(e),g(!0)},C=s.filter(e=>{let t="".concat(e.customer_name," ").concat(e.customer_family_name).toLowerCase(),s=e.product_name.toLowerCase(),r=d.toLowerCase();return t.includes(r)||s.includes(r)}),k=C.reduce((e,t)=>{let s="".concat(t.customer_name," ").concat(t.customer_family_name);return e[s]||(e[s]=[]),e[s].push(t),e},{});return c?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search by customer or product...",value:d,onChange:e=>b(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("button",{onClick:()=>g(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Add Debt Record"]})]}),(0,r.jsx)("div",{className:"space-y-6",children:Object.entries(k).map(e=>{let[t,s]=e,a=s.reduce((e,t)=>e+t.total_amount,0);return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{className:"h-5 w-5 text-gray-400 mr-2"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:t})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[s.length," item(s)"]}),(0,r.jsxs)("p",{className:"text-lg font-bold text-red-600",children:["₱",a.toFixed(2)]})]})]})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:s.map(e=>(0,r.jsx)("div",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.product_name}),(0,r.jsxs)("div",{className:"mt-1 text-sm text-gray-600 space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("span",{children:["Quantity: ",e.quantity]}),(0,r.jsx)("span",{className:"mx-2",children:"•"}),(0,r.jsxs)("span",{children:["Unit Price: ₱",e.product_price.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-1"}),(0,r.jsxs)("span",{children:["Date: ",(0,N.GP)(new Date(e.debt_date),"MMM dd, yyyy")]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("p",{className:"font-semibold text-gray-900",children:["₱",e.total_amount.toFixed(2)]})}),(0,r.jsx)("button",{onClick:()=>_(e),className:"p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>w(e.id),className:"p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})]})]})},e.id))})]},t)})}),0===C.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(n.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No debt records found"}),(0,r.jsx)("p",{className:"text-gray-600",children:d?"Try adjusting your search criteria":"Get started by adding your first debt record"})]}),(0,r.jsx)(j,{isOpen:h,onClose:()=>{g(!1),y(null),v(),t()},debt:p})]})}var w=s(5868),_=s(1243);function C(e){let{stats:t}=e,s=[{title:"Products in List",value:t.totalProducts,icon:c.A,color:"bg-blue-500",textColor:"text-blue-600",bgColor:"bg-blue-50"},{title:"Customer Debts",value:t.totalDebts,icon:n.A,color:"bg-green-500",textColor:"text-green-600",bgColor:"bg-green-50"},{title:"Total Debt Amount",value:"₱".concat(t.totalDebtAmount.toFixed(2)),icon:w.A,color:"bg-yellow-500",textColor:"text-yellow-600",bgColor:"bg-yellow-50"},{title:"Low Stock Items",value:t.lowStockProducts,icon:_.A,color:"bg-red-500",textColor:"text-red-600",bgColor:"bg-red-50"}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:s.map((e,t)=>{let s=e.icon;return(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 rounded-lg ".concat(e.bgColor),children:(0,r.jsx)(s,{className:"h-6 w-6 ".concat(e.textColor)})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:e.value})]})]})},t)})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("button",{className:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Add to Product List"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Add a new product to your list"})]})]}),(0,r.jsxs)("button",{className:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(n.A,{className:"h-8 w-8 text-green-600 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Record New Debt"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Add a new customer debt record"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Store Overview"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Products in List"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:t.totalProducts})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Outstanding Debts"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:t.totalDebts})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total Amount Owed"}),(0,r.jsxs)("span",{className:"font-semibold text-gray-900",children:["₱",t.totalDebtAmount.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Items Need Restocking"}),(0,r.jsx)("span",{className:"font-semibold ".concat(t.lowStockProducts>0?"text-red-600":"text-green-600"),children:t.lowStockProducts})]})]})]})]})}function k(){let[e,t]=(0,a.useState)("dashboard"),[s,l]=(0,a.useState)({totalProducts:0,totalDebts:0,totalDebtAmount:0,lowStockProducts:0});(0,a.useEffect)(()=>{c()},[]);let c=async()=>{try{let e=await fetch("/api/products"),t=(await e.json()).products||[],s=await fetch("/api/debts"),r=(await s.json()).debts||[],a=r.reduce((e,t)=>e+t.total_amount,0),c=t.filter(e=>e.stock_quantity<10).length;l({totalProducts:t.length,totalDebts:r.length,totalDebtAmount:a,lowStockProducts:c})}catch(e){console.error("Error fetching stats:",e)}};return(0,r.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,r.jsx)(d,{activeSection:e,setActiveSection:t}),(0,r.jsx)("main",{className:"flex-1 overflow-auto",children:(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["dashboard"===e&&"Dashboard","products"===e&&"Product Lists","debts"===e&&"Customer Debt Management"]}),(0,r.jsxs)("p",{className:"text-gray-600 mt-2",children:["dashboard"===e&&"Overview of your Sari-Sari store","products"===e&&"Manage your product lists with CRUD operations","debts"===e&&"Track customer debt and payments"]})]}),(()=>{switch(e){case"products":return(0,r.jsx)(y,{onStatsUpdate:c});case"debts":return(0,r.jsx)(v,{onStatsUpdate:c});default:return(0,r.jsx)(C,{stats:s})}})()]})})]})}},4716:(e,t,s)=>{Promise.resolve().then(s.bind(s,2202))}},e=>{var t=t=>e(e.s=t);e.O(0,[712,441,684,358],()=>t(4716)),_N_E=e.O()}]);