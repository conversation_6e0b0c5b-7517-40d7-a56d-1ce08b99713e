'use client'

import { useState, useEffect } from 'react'
import Sidebar from '@/components/Sidebar'
import ProductsSection from '@/components/ProductsSection'
import DebtsSection from '@/components/DebtsSection'
import DashboardStats from '@/components/DashboardStats'

export default function Home() {
  const [activeSection, setActiveSection] = useState('dashboard')
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalDebts: 0,
    totalDebtAmount: 0,
    lowStockProducts: 0
  })

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      // Fetch products
      const productsRes = await fetch('/api/products')
      const productsData = await productsRes.json()
      const products = productsData.products || []

      // Fetch debts
      const debtsRes = await fetch('/api/debts')
      const debtsData = await debtsRes.json()
      const debts = debtsData.debts || []

      // Calculate stats
      const totalDebtAmount = debts.reduce((sum: number, debt: { total_amount: number }) => sum + debt.total_amount, 0)
      const lowStockProducts = products.filter((product: { stock_quantity: number }) => product.stock_quantity < 10).length

      setStats({
        totalProducts: products.length,
        totalDebts: debts.length,
        totalDebtAmount,
        lowStockProducts
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const renderContent = () => {
    switch (activeSection) {
      case 'products':
        return <ProductsSection onStatsUpdate={fetchStats} />
      case 'debts':
        return <DebtsSection onStatsUpdate={fetchStats} />
      default:
        return <DashboardStats stats={stats} />
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />
      <main className="flex-1 overflow-auto">
        <div className="p-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              {activeSection === 'dashboard' && 'Dashboard'}
              {activeSection === 'products' && 'Product Lists'}
              {activeSection === 'debts' && 'Customer Debt Management'}
            </h1>
            <p className="text-gray-600 mt-2">
              {activeSection === 'dashboard' && 'Overview of your Sari-Sari store'}
              {activeSection === 'products' && 'Manage your product lists with CRUD operations'}
              {activeSection === 'debts' && 'Track customer debt and payments'}
            </p>
          </div>
          {renderContent()}
        </div>
      </main>
    </div>
  )
}
