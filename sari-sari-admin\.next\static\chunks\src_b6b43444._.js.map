{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { LayoutDashboard, Package, Users, Store } from 'lucide-react'\n\ninterface SidebarProps {\n  activeSection: string\n  setActiveSection: (section: string) => void\n}\n\nexport default function Sidebar({ activeSection, setActiveSection }: SidebarProps) {\n  const menuItems = [\n    {\n      id: 'dashboard',\n      label: 'Dashboard',\n      icon: LayoutDashboard,\n    },\n    {\n      id: 'products',\n      label: 'Products',\n      icon: Package,\n    },\n    {\n      id: 'debts',\n      label: 'Customer Debts',\n      icon: Users,\n    },\n  ]\n\n  return (\n    <div className=\"w-64 bg-white shadow-lg\">\n      <div className=\"p-6\">\n        <div className=\"flex items-center space-x-2\">\n          <Store className=\"h-8 w-8 text-blue-600\" />\n          <h1 className=\"text-xl font-bold text-gray-900\"><PERSON>ri-<PERSON><PERSON></h1>\n        </div>\n      </div>\n      \n      <nav className=\"mt-6\">\n        <div className=\"px-3\">\n          {menuItems.map((item) => {\n            const Icon = item.icon\n            const isActive = activeSection === item.id\n            \n            return (\n              <button\n                key={item.id}\n                onClick={() => setActiveSection(item.id)}\n                className={`w-full flex items-center px-3 py-2 mb-1 text-left rounded-lg transition-colors ${\n                  isActive\n                    ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'\n                }`}\n              >\n                <Icon className=\"h-5 w-5 mr-3\" />\n                {item.label}\n              </button>\n            )\n          })}\n        </div>\n      </nav>\n      \n      <div className=\"absolute bottom-0 w-64 p-6 border-t border-gray-200\">\n        <div className=\"text-sm text-gray-500\">\n          <p>Sari-Sari Store</p>\n          <p>Admin Dashboard v1.0</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AASe,SAAS,QAAQ,EAAE,aAAa,EAAE,gBAAgB,EAAgB;IAC/E,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;;;;;;sCACjB,6LAAC;4BAAG,WAAU;sCAAkC;;;;;;;;;;;;;;;;;0BAIpD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC;wBACd,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,WAAW,kBAAkB,KAAK,EAAE;wBAE1C,qBACE,6LAAC;4BAEC,SAAS,IAAM,iBAAiB,KAAK,EAAE;4BACvC,WAAW,CAAC,+EAA+E,EACzF,WACI,yDACA,uDACJ;;8CAEF,6LAAC;oCAAK,WAAU;;;;;;gCACf,KAAK,KAAK;;2BATN,KAAK,EAAE;;;;;oBAYlB;;;;;;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb;KA5DwB", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/components/ProductModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Upload, Package } from 'lucide-react'\nimport { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\n\ninterface ProductModalProps {\n  isOpen: boolean\n  onClose: () => void\n  product?: Product | null\n}\n\nexport default function ProductModal({ isOpen, onClose, product }: ProductModalProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    net_weight: '',\n    price: '',\n    stock_quantity: '',\n    category: '',\n    image_url: ''\n  })\n  const [imageFile, setImageFile] = useState<File | null>(null)\n  const [imagePreview, setImagePreview] = useState<string>('')\n  const [loading, setLoading] = useState(false)\n  const [uploading, setUploading] = useState(false)\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        net_weight: product.net_weight,\n        price: product.price.toString(),\n        stock_quantity: product.stock_quantity.toString(),\n        category: product.category,\n        image_url: product.image_url || ''\n      })\n      setImagePreview(product.image_url || '')\n    } else {\n      setFormData({\n        name: '',\n        net_weight: '',\n        price: '',\n        stock_quantity: '',\n        category: '',\n        image_url: ''\n      })\n      setImagePreview('')\n    }\n    setImageFile(null)\n  }, [product, isOpen])\n\n  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0]\n    if (file) {\n      setImageFile(file)\n      const reader = new FileReader()\n      reader.onloadend = () => {\n        setImagePreview(reader.result as string)\n      }\n      reader.readAsDataURL(file)\n    }\n  }\n\n  const uploadImage = async (): Promise<string> => {\n    if (!imageFile) return formData.image_url\n\n    setUploading(true)\n    try {\n      const uploadFormData = new FormData()\n      uploadFormData.append('file', imageFile)\n\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: uploadFormData,\n      })\n\n      const data = await response.json()\n      return data.url\n    } catch (error) {\n      console.error('Error uploading image:', error)\n      return formData.image_url\n    } finally {\n      setUploading(false)\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      // Upload image if there's a new one\n      const imageUrl = await uploadImage()\n\n      const productData = {\n        ...formData,\n        image_url: imageUrl,\n        price: parseFloat(formData.price),\n        stock_quantity: parseInt(formData.stock_quantity)\n      }\n\n      const url = product ? `/api/products/${product.id}` : '/api/products'\n      const method = product ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(productData),\n      })\n\n      if (response.ok) {\n        onClose()\n      } else {\n        console.error('Error saving product')\n      }\n    } catch (error) {\n      console.error('Error saving product:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold\">\n            {product ? 'Edit Product' : 'Add New Product'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Image Upload */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Product Image\n            </label>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-2\">\n                {imagePreview ? (\n                  <img\n                    src={imagePreview}\n                    alt=\"Preview\"\n                    className=\"w-full h-full object-cover rounded-lg\"\n                  />\n                ) : (\n                  <Package className=\"h-12 w-12 text-gray-400\" />\n                )}\n              </div>\n              <input\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleImageChange}\n                className=\"hidden\"\n                id=\"image-upload\"\n              />\n              <label\n                htmlFor=\"image-upload\"\n                className=\"flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50\"\n              >\n                <Upload className=\"h-4 w-4 mr-2\" />\n                Choose Image\n              </label>\n            </div>\n          </div>\n\n          {/* Product Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.name}\n              onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Net Weight */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Net Weight *\n            </label>\n            <input\n              type=\"text\"\n              required\n              placeholder=\"e.g., 100g, 1L, 250ml\"\n              value={formData.net_weight}\n              onChange={(e) => setFormData({ ...formData, net_weight: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Price */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Price (₱) *\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              required\n              value={formData.price}\n              onChange={(e) => setFormData({ ...formData, price: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Stock Quantity */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Stock Quantity *\n            </label>\n            <input\n              type=\"number\"\n              min=\"0\"\n              required\n              value={formData.stock_quantity}\n              onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Category */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Category *\n            </label>\n            <select\n              required\n              value={formData.category}\n              onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Select Category</option>\n              {PRODUCT_CATEGORIES.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading || uploading}\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading || uploading ? 'Saving...' : (product ? 'Update' : 'Add Product')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;AAFA;;;;AAYe,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAqB;;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,YAAY;QACZ,OAAO;QACP,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS;gBACX,YAAY;oBACV,MAAM,QAAQ,IAAI;oBAClB,YAAY,QAAQ,UAAU;oBAC9B,OAAO,QAAQ,KAAK,CAAC,QAAQ;oBAC7B,gBAAgB,QAAQ,cAAc,CAAC,QAAQ;oBAC/C,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS,IAAI;gBAClC;gBACA,gBAAgB,QAAQ,SAAS,IAAI;YACvC,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,YAAY;oBACZ,OAAO;oBACP,gBAAgB;oBAChB,UAAU;oBACV,WAAW;gBACb;gBACA,gBAAgB;YAClB;YACA,aAAa;QACf;iCAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,aAAa;YACb,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,gBAAgB,OAAO,MAAM;YAC/B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,OAAO,SAAS,SAAS;QAEzC,aAAa;QACb,IAAI;YACF,MAAM,iBAAiB,IAAI;YAC3B,eAAe,MAAM,CAAC,QAAQ;YAE9B,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO,SAAS,SAAS;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,oCAAoC;YACpC,MAAM,WAAW,MAAM;YAEvB,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,WAAW;gBACX,OAAO,WAAW,SAAS,KAAK;gBAChC,gBAAgB,SAAS,SAAS,cAAc;YAClD;YAEA,MAAM,MAAM,UAAU,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,GAAG;YACtD,MAAM,SAAS,UAAU,QAAQ;YAEjC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,UAAU,iBAAiB;;;;;;sCAE9B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,6BACC,6LAAC;gDACC,KAAK;gDACL,KAAI;gDACJ,WAAU;;;;;qEAGZ,6LAAC;gDAAQ,WAAU;;;;;;;;;;;sDAGvB,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;4CACV,IAAG;;;;;;sDAEL,6LAAC;4CACC,SAAQ;4CACR,WAAU;;8DAEV,6LAAC;oDAAO,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,aAAY;oCACZ,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACvE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAClE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,cAAc;oCAC9B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC3E,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,mBAAmB,GAAG,CAAC,CAAA,yBACtB,6LAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU,WAAW;oCACrB,WAAU;8CAET,WAAW,YAAY,cAAe,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1E;GAvQwB;KAAA", "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/components/ProductsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport ProductModal from './ProductModal'\nimport { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\n\ninterface ProductsSectionProps {\n  onStatsUpdate: () => void\n}\n\nexport default function ProductsSection({ onStatsUpdate }: ProductsSectionProps) {\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null)\n\n  useEffect(() => {\n    fetchProducts()\n  }, [])\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products')\n      const data = await response.json()\n      setProducts(data.products || [])\n    } catch (error) {\n      console.error('Error fetching products:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this product?')) return\n\n    try {\n      const response = await fetch(`/api/products/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setProducts(products.filter(p => p.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error)\n    }\n  }\n\n  const handleEdit = (product: Product) => {\n    setEditingProduct(product)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingProduct(null)\n    fetchProducts()\n    onStatsUpdate()\n  }\n\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesCategory = selectedCategory === '' || product.category === selectedCategory\n    return matchesSearch && matchesCategory\n  })\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div className=\"flex space-x-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">All Categories</option>\n            {PRODUCT_CATEGORIES.map(category => (\n              <option key={category} value={category}>{category}</option>\n            ))}\n          </select>\n        </div>\n        <button\n          onClick={() => setIsModalOpen(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Product\n        </button>\n      </div>\n\n      {/* Products Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {filteredProducts.map((product) => (\n          <div key={product.id} className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n            <div className=\"aspect-square bg-gray-100 flex items-center justify-center\">\n              {product.image_url ? (\n                <img\n                  src={product.image_url}\n                  alt={product.name}\n                  className=\"w-full h-full object-cover\"\n                />\n              ) : (\n                <Package className=\"h-16 w-16 text-gray-400\" />\n              )}\n            </div>\n            <div className=\"p-4\">\n              <h3 className=\"font-semibold text-gray-900 mb-1\">{product.name}</h3>\n              <p className=\"text-sm text-gray-600 mb-2\">{product.category}</p>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-lg font-bold text-green-600\">₱{product.price}</span>\n                <span className=\"text-sm text-gray-500\">{product.net_weight}</span>\n              </div>\n              <div className=\"flex justify-between items-center mb-4\">\n                <span className={`text-sm ${product.stock_quantity < 10 ? 'text-red-600' : 'text-gray-600'}`}>\n                  Stock: {product.stock_quantity}\n                </span>\n                {product.stock_quantity < 10 && (\n                  <span className=\"text-xs bg-red-100 text-red-800 px-2 py-1 rounded\">Low Stock</span>\n                )}\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => handleEdit(product)}\n                  className=\"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\"\n                >\n                  <Edit className=\"h-4 w-4 mr-1\" />\n                  Edit\n                </button>\n                <button\n                  onClick={() => handleDelete(product.id)}\n                  className=\"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors\"\n                >\n                  <Trash2 className=\"h-4 w-4 mr-1\" />\n                  Delete\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {filteredProducts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No products found</h3>\n          <p className=\"text-gray-600\">\n            {searchTerm || selectedCategory\n              ? 'Try adjusting your search or filter criteria'\n              : 'Get started by adding your first product'}\n          </p>\n        </div>\n      )}\n\n      {/* Product Modal */}\n      <ProductModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        product={editingProduct}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAEA;;;;;;;;AAJA;;;;;AAWe,SAAS,gBAAgB,EAAE,aAAa,EAAwB;;IAC7E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,QAAQ,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,kDAAkD;QAE/D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAClD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC1C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,kBAAkB;QAClB;QACA;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAChF,MAAM,kBAAkB,qBAAqB,MAAM,QAAQ,QAAQ,KAAK;QACxE,OAAO,iBAAiB;IAC1B;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAGd,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,mBAAmB,GAAG,CAAC,CAAA,yBACtB,6LAAC;4CAAsB,OAAO;sDAAW;2CAA5B;;;;;;;;;;;;;;;;;kCAInB,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC;gCAAK,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wBAAqB,WAAU;;0CAC9B,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,SAAS,iBAChB,6LAAC;oCACC,KAAK,QAAQ,SAAS;oCACtB,KAAK,QAAQ,IAAI;oCACjB,WAAU;;;;;yDAGZ,6LAAC;oCAAQ,WAAU;;;;;;;;;;;0CAGvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC,QAAQ,IAAI;;;;;;kDAC9D,6LAAC;wCAAE,WAAU;kDAA8B,QAAQ,QAAQ;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;oDAAmC;oDAAE,QAAQ,KAAK;;;;;;;0DAClE,6LAAC;gDAAK,WAAU;0DAAyB,QAAQ,UAAU;;;;;;;;;;;;kDAE7D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,cAAc,GAAG,KAAK,iBAAiB,iBAAiB;;oDAAE;oDACpF,QAAQ,cAAc;;;;;;;4CAE/B,QAAQ,cAAc,GAAG,oBACxB,6LAAC;gDAAK,WAAU;0DAAoD;;;;;;;;;;;;kDAGxE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,WAAW;gDAC1B,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC;gDACC,SAAS,IAAM,aAAa,QAAQ,EAAE;gDACtC,WAAU;;kEAEV,6LAAC;wDAAO,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBAvCjC,QAAQ,EAAE;;;;;;;;;;YAgDvB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAQ,WAAU;;;;;;kCACnB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,mBACX,iDACA;;;;;;;;;;;;0BAMV,6LAAC,qIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,SAAS;;;;;;;;;;;;AAIjB;GA7KwB;KAAA", "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/components/DebtModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X } from 'lucide-react'\nimport { CustomerDebt } from '@/lib/supabase'\n\ninterface DebtModalProps {\n  isOpen: boolean\n  onClose: () => void\n  debt?: CustomerDebt | null\n}\n\nexport default function DebtModal({ isOpen, onClose, debt }: DebtModalProps) {\n  const [formData, setFormData] = useState({\n    customer_name: '',\n    customer_family_name: '',\n    product_name: '',\n    product_price: '',\n    quantity: '',\n    debt_date: ''\n  })\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    if (debt) {\n      setFormData({\n        customer_name: debt.customer_name,\n        customer_family_name: debt.customer_family_name,\n        product_name: debt.product_name,\n        product_price: debt.product_price.toString(),\n        quantity: debt.quantity.toString(),\n        debt_date: debt.debt_date\n      })\n    } else {\n      setFormData({\n        customer_name: '',\n        customer_family_name: '',\n        product_name: '',\n        product_price: '',\n        quantity: '',\n        debt_date: new Date().toISOString().split('T')[0]\n      })\n    }\n  }, [debt, isOpen])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      const debtData = {\n        ...formData,\n        product_price: parseFloat(formData.product_price),\n        quantity: parseInt(formData.quantity)\n      }\n\n      const url = debt ? `/api/debts/${debt.id}` : '/api/debts'\n      const method = debt ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(debtData),\n      })\n\n      if (response.ok) {\n        onClose()\n      } else {\n        console.error('Error saving debt record')\n      }\n    } catch (error) {\n      console.error('Error saving debt record:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  const totalAmount = formData.product_price && formData.quantity \n    ? (parseFloat(formData.product_price) * parseInt(formData.quantity)).toFixed(2)\n    : '0.00'\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold\">\n            {debt ? 'Edit Debt Record' : 'Add New Debt Record'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Customer Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Customer First Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.customer_name}\n              onChange={(e) => setFormData({ ...formData, customer_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Juan\"\n            />\n          </div>\n\n          {/* Customer Family Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Customer Family Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.customer_family_name}\n              onChange={(e) => setFormData({ ...formData, customer_family_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Dela Cruz\"\n            />\n          </div>\n\n          {/* Product Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.product_name}\n              onChange={(e) => setFormData({ ...formData, product_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Lucky Me Pancit Canton\"\n            />\n          </div>\n\n          {/* Product Price */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Price (₱) *\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              required\n              value={formData.product_price}\n              onChange={(e) => setFormData({ ...formData, product_price: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"0.00\"\n            />\n          </div>\n\n          {/* Quantity */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Quantity *\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              required\n              value={formData.quantity}\n              onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"1\"\n            />\n          </div>\n\n          {/* Debt Date */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Debt Date *\n            </label>\n            <input\n              type=\"date\"\n              required\n              value={formData.debt_date}\n              onChange={(e) => setFormData({ ...formData, debt_date: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Total Amount Display */}\n          <div className=\"bg-gray-50 p-3 rounded-md\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm font-medium text-gray-700\">Total Amount:</span>\n              <span className=\"text-lg font-bold text-green-600\">₱{totalAmount}</span>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading ? 'Saving...' : (debt ? 'Update' : 'Add Record')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;AAFA;;;AAYe,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAkB;;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,eAAe;QACf,sBAAsB;QACtB,cAAc;QACd,eAAe;QACf,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,eAAe,KAAK,aAAa;oBACjC,sBAAsB,KAAK,oBAAoB;oBAC/C,cAAc,KAAK,YAAY;oBAC/B,eAAe,KAAK,aAAa,CAAC,QAAQ;oBAC1C,UAAU,KAAK,QAAQ,CAAC,QAAQ;oBAChC,WAAW,KAAK,SAAS;gBAC3B;YACF,OAAO;gBACL,YAAY;oBACV,eAAe;oBACf,sBAAsB;oBACtB,cAAc;oBACd,eAAe;oBACf,UAAU;oBACV,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACnD;YACF;QACF;8BAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW;gBACf,GAAG,QAAQ;gBACX,eAAe,WAAW,SAAS,aAAa;gBAChD,UAAU,SAAS,SAAS,QAAQ;YACtC;YAEA,MAAM,MAAM,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,GAAG;YAC7C,MAAM,SAAS,OAAO,QAAQ;YAE9B,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc,SAAS,aAAa,IAAI,SAAS,QAAQ,GAC3D,CAAC,WAAW,SAAS,aAAa,IAAI,SAAS,SAAS,QAAQ,CAAC,EAAE,OAAO,CAAC,KAC3E;IAEJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,OAAO,qBAAqB;;;;;;sCAE/B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC1E,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,oBAAoB;oCACpC,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,sBAAsB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjF,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC1E,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACtE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAK,WAAU;;4CAAmC;4CAAE;;;;;;;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,cAAe,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1D;GAlNwB;KAAA", "debugId": null}}, {"offset": {"line": 1448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/components/DebtsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport DebtModal from './DebtModal'\nimport { CustomerDebt } from '@/lib/supabase'\nimport { format } from 'date-fns'\n\ninterface DebtsSectionProps {\n  onStatsUpdate: () => void\n}\n\nexport default function DebtsSection({ onStatsUpdate }: DebtsSectionProps) {\n  const [debts, setDebts] = useState<CustomerDebt[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null)\n\n  useEffect(() => {\n    fetchDebts()\n  }, [])\n\n  const fetchDebts = async () => {\n    try {\n      const response = await fetch('/api/debts')\n      const data = await response.json()\n      setDebts(data.debts || [])\n    } catch (error) {\n      console.error('Error fetching debts:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this debt record?')) return\n\n    try {\n      const response = await fetch(`/api/debts/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setDebts(debts.filter(d => d.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting debt:', error)\n    }\n  }\n\n  const handleEdit = (debt: CustomerDebt) => {\n    setEditingDebt(debt)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingDebt(null)\n    fetchDebts()\n    onStatsUpdate()\n  }\n\n  const filteredDebts = debts.filter(debt => {\n    const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()\n    const productName = debt.product_name.toLowerCase()\n    const search = searchTerm.toLowerCase()\n    return customerName.includes(search) || productName.includes(search)\n  })\n\n  // Group debts by customer\n  const groupedDebts = filteredDebts.reduce((acc, debt) => {\n    const customerKey = `${debt.customer_name} ${debt.customer_family_name}`\n    if (!acc[customerKey]) {\n      acc[customerKey] = []\n    }\n    acc[customerKey].push(debt)\n    return acc\n  }, {} as Record<string, CustomerDebt[]>)\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search by customer or product...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n        <button\n          onClick={() => setIsModalOpen(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Debt Record\n        </button>\n      </div>\n\n      {/* Debts List */}\n      <div className=\"space-y-6\">\n        {Object.entries(groupedDebts).map(([customerName, customerDebts]) => {\n          const totalAmount = customerDebts.reduce((sum, debt) => sum + debt.total_amount, 0)\n          \n          return (\n            <div key={customerName} className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              <div className=\"bg-gray-50 px-6 py-4 border-b border-gray-200\">\n                <div className=\"flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <Users className=\"h-5 w-5 text-gray-400 mr-2\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900\">{customerName}</h3>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm text-gray-600\">{customerDebts.length} item(s)</p>\n                    <p className=\"text-lg font-bold text-red-600\">₱{totalAmount.toFixed(2)}</p>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"divide-y divide-gray-200\">\n                {customerDebts.map((debt) => (\n                  <div key={debt.id} className=\"px-6 py-4\">\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-medium text-gray-900\">{debt.product_name}</h4>\n                        <div className=\"mt-1 text-sm text-gray-600 space-y-1\">\n                          <div className=\"flex items-center\">\n                            <span>Quantity: {debt.quantity}</span>\n                            <span className=\"mx-2\">•</span>\n                            <span>Unit Price: ₱{debt.product_price.toFixed(2)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"h-4 w-4 mr-1\" />\n                            <span>Date: {format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2 ml-4\">\n                        <div className=\"text-right\">\n                          <p className=\"font-semibold text-gray-900\">₱{debt.total_amount.toFixed(2)}</p>\n                        </div>\n                        <button\n                          onClick={() => handleEdit(debt)}\n                          className=\"p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors\"\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDelete(debt.id)}\n                          className=\"p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {filteredDebts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Users className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No debt records found</h3>\n          <p className=\"text-gray-600\">\n            {searchTerm\n              ? 'Try adjusting your search criteria'\n              : 'Get started by adding your first debt record'}\n          </p>\n        </div>\n      )}\n\n      {/* Debt Modal */}\n      <DebtModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        debt={editingDebt}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAEA;;;;;;;;AAJA;;;;;AAYe,SAAS,aAAa,EAAE,aAAa,EAAqB;;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK,IAAI,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,sDAAsD;QAEnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;QACf;QACA;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,eAAe,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE,CAAC,WAAW;QACrF,MAAM,cAAc,KAAK,YAAY,CAAC,WAAW;QACjD,MAAM,SAAS,WAAW,WAAW;QACrC,OAAO,aAAa,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC;IAC/D;IAEA,0BAA0B;IAC1B,MAAM,eAAe,cAAc,MAAM,CAAC,CAAC,KAAK;QAC9C,MAAM,cAAc,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE;QACxE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;YACrB,GAAG,CAAC,YAAY,GAAG,EAAE;QACvB;QACA,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG,CAAC;IAEJ,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAGd,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC;gCAAK,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,cAAc,cAAc;oBAC9D,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;oBAEjF,qBACE,6LAAC;wBAAuB,WAAU;;0CAChC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;;;;;8DACjB,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDAAyB,cAAc,MAAM;wDAAC;;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;;wDAAiC;wDAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK1E,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wCAAkB,WAAU;kDAC3B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B,KAAK,YAAY;;;;;;sEAC5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;gFAAK;gFAAW,KAAK,QAAQ;;;;;;;sFAC9B,6LAAC;4EAAK,WAAU;sFAAO;;;;;;sFACvB,6LAAC;;gFAAK;gFAAc,KAAK,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;8EAEjD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAS,WAAU;;;;;;sFACpB,6LAAC;;gFAAK;gFAAO,OAAO,IAAI,KAAK,KAAK,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;8DAIpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;oEAA8B;oEAAE,KAAK,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;sEAEzE,6LAAC;4DACC,SAAS,IAAM,WAAW;4DAC1B,WAAU;sEAEV,cAAA,6LAAC;gEAAK,WAAU;;;;;;;;;;;sEAElB,6LAAC;4DACC,SAAS,IAAM,aAAa,KAAK,EAAE;4DACnC,WAAU;sEAEV,cAAA,6LAAC;gEAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCA9BhB,KAAK,EAAE;;;;;;;;;;;uBAhBb;;;;;gBAuDd;;;;;;YAGD,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,aACG,uCACA;;;;;;;;;;;;0BAMV,6LAAC,kIAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS;gBACT,MAAM;;;;;;;;;;;;AAId;GAxLwB;KAAA", "debugId": null}}, {"offset": {"line": 1926, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/components/DashboardStats.tsx"], "sourcesContent": ["'use client'\n\nimport { Package, Users, DollarSign, AlertTriangle } from 'lucide-react'\n\ninterface DashboardStatsProps {\n  stats: {\n    totalProducts: number\n    totalDebts: number\n    totalDebtAmount: number\n    lowStockProducts: number\n  }\n}\n\nexport default function DashboardStats({ stats }: DashboardStatsProps) {\n  const statCards = [\n    {\n      title: 'Total Products',\n      value: stats.totalProducts,\n      icon: Package,\n      color: 'bg-blue-500',\n      textColor: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n    },\n    {\n      title: 'Customer Debts',\n      value: stats.totalDebts,\n      icon: Users,\n      color: 'bg-green-500',\n      textColor: 'text-green-600',\n      bgColor: 'bg-green-50',\n    },\n    {\n      title: 'Total Debt Amount',\n      value: `₱${stats.totalDebtAmount.toFixed(2)}`,\n      icon: DollarSign,\n      color: 'bg-yellow-500',\n      textColor: 'text-yellow-600',\n      bgColor: 'bg-yellow-50',\n    },\n    {\n      title: 'Low Stock Items',\n      value: stats.lowStockProducts,\n      icon: AlertTriangle,\n      color: 'bg-red-500',\n      textColor: 'text-red-600',\n      bgColor: 'bg-red-50',\n    },\n  ]\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statCards.map((card, index) => {\n          const Icon = card.icon\n          return (\n            <div key={index} className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className={`p-3 rounded-lg ${card.bgColor}`}>\n                  <Icon className={`h-6 w-6 ${card.textColor}`} />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">{card.title}</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">{card.value}</p>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <button className=\"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n            <Package className=\"h-8 w-8 text-blue-600 mr-3\" />\n            <div className=\"text-left\">\n              <p className=\"font-medium text-gray-900\">Add New Product</p>\n              <p className=\"text-sm text-gray-600\">Add a new product to your inventory</p>\n            </div>\n          </button>\n          <button className=\"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n            <Users className=\"h-8 w-8 text-green-600 mr-3\" />\n            <div className=\"text-left\">\n              <p className=\"font-medium text-gray-900\">Record New Debt</p>\n              <p className=\"text-sm text-gray-600\">Add a new customer debt record</p>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Store Overview</h3>\n        <div className=\"space-y-4\">\n          <div className=\"flex justify-between items-center py-2 border-b border-gray-100\">\n            <span className=\"text-gray-600\">Products in Stock</span>\n            <span className=\"font-semibold text-gray-900\">{stats.totalProducts}</span>\n          </div>\n          <div className=\"flex justify-between items-center py-2 border-b border-gray-100\">\n            <span className=\"text-gray-600\">Outstanding Debts</span>\n            <span className=\"font-semibold text-gray-900\">{stats.totalDebts}</span>\n          </div>\n          <div className=\"flex justify-between items-center py-2 border-b border-gray-100\">\n            <span className=\"text-gray-600\">Total Amount Owed</span>\n            <span className=\"font-semibold text-gray-900\">₱{stats.totalDebtAmount.toFixed(2)}</span>\n          </div>\n          <div className=\"flex justify-between items-center py-2\">\n            <span className=\"text-gray-600\">Items Need Restocking</span>\n            <span className={`font-semibold ${stats.lowStockProducts > 0 ? 'text-red-600' : 'text-green-600'}`}>\n              {stats.lowStockProducts}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAae,SAAS,eAAe,EAAE,KAAK,EAAuB;IACnE,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,MAAM;YACN,OAAO;YACP,WAAW;YACX,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,MAAM;YACN,OAAO;YACP,WAAW;YACX,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAC,CAAC,EAAE,MAAM,eAAe,CAAC,OAAO,CAAC,IAAI;YAC7C,MAAM;YACN,OAAO;YACP,WAAW;YACX,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,gBAAgB;YAC7B,MAAM;YACN,OAAO;YACP,WAAW;YACX,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM;oBACpB,MAAM,OAAO,KAAK,IAAI;oBACtB,qBACE,6LAAC;wBAAgB,WAAU;kCACzB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE;8CAC9C,cAAA,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAqC,KAAK,KAAK;;;;;;sDAC5D,6LAAC;4CAAE,WAAU;sDAAwC,KAAK,KAAK;;;;;;;;;;;;;;;;;;uBAP3D;;;;;gBAYd;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCAAQ,WAAU;;;;;;kDACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA4B;;;;;;0DACzC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCAAM,WAAU;;;;;;kDACjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA4B;;;;;;0DACzC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAA+B,MAAM,aAAa;;;;;;;;;;;;0CAEpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAA+B,MAAM,UAAU;;;;;;;;;;;;0CAEjE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;;4CAA8B;4CAAE,MAAM,eAAe,CAAC,OAAO,CAAC;;;;;;;;;;;;;0CAEhF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAW,CAAC,cAAc,EAAE,MAAM,gBAAgB,GAAG,IAAI,iBAAiB,kBAAkB;kDAC/F,MAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;KAzGwB", "debugId": null}}, {"offset": {"line": 2297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/sari-sari-admin/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Package, Users, DollarSign, ShoppingCart } from 'lucide-react'\nimport Sidebar from '@/components/Sidebar'\nimport ProductsSection from '@/components/ProductsSection'\nimport DebtsSection from '@/components/DebtsSection'\nimport DashboardStats from '@/components/DashboardStats'\n\nexport default function Home() {\n  const [activeSection, setActiveSection] = useState('dashboard')\n  const [stats, setStats] = useState({\n    totalProducts: 0,\n    totalDebts: 0,\n    totalDebtAmount: 0,\n    lowStockProducts: 0\n  })\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  const fetchStats = async () => {\n    try {\n      // Fetch products\n      const productsRes = await fetch('/api/products')\n      const productsData = await productsRes.json()\n      const products = productsData.products || []\n\n      // Fetch debts\n      const debtsRes = await fetch('/api/debts')\n      const debtsData = await debtsRes.json()\n      const debts = debtsData.debts || []\n\n      // Calculate stats\n      const totalDebtAmount = debts.reduce((sum: number, debt: any) => sum + debt.total_amount, 0)\n      const lowStockProducts = products.filter((product: any) => product.stock_quantity < 10).length\n\n      setStats({\n        totalProducts: products.length,\n        totalDebts: debts.length,\n        totalDebtAmount,\n        lowStockProducts\n      })\n    } catch (error) {\n      console.error('Error fetching stats:', error)\n    }\n  }\n\n  const renderContent = () => {\n    switch (activeSection) {\n      case 'products':\n        return <ProductsSection onStatsUpdate={fetchStats} />\n      case 'debts':\n        return <DebtsSection onStatsUpdate={fetchStats} />\n      default:\n        return <DashboardStats stats={stats} />\n    }\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />\n      <main className=\"flex-1 overflow-auto\">\n        <div className=\"p-8\">\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              {activeSection === 'dashboard' && 'Dashboard'}\n              {activeSection === 'products' && 'Product Management'}\n              {activeSection === 'debts' && 'Customer Debt Management'}\n            </h1>\n            <p className=\"text-gray-600 mt-2\">\n              {activeSection === 'dashboard' && 'Overview of your Sari-Sari store'}\n              {activeSection === 'products' && 'Manage your product inventory'}\n              {activeSection === 'debts' && 'Track customer debt and payments'}\n            </p>\n          </div>\n          {renderContent()}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;;;AAPA;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,eAAe;QACf,YAAY;QACZ,iBAAiB;QACjB,kBAAkB;IACpB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,iBAAiB;YACjB,MAAM,cAAc,MAAM,MAAM;YAChC,MAAM,eAAe,MAAM,YAAY,IAAI;YAC3C,MAAM,WAAW,aAAa,QAAQ,IAAI,EAAE;YAE5C,cAAc;YACd,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,QAAQ,UAAU,KAAK,IAAI,EAAE;YAEnC,kBAAkB;YAClB,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,KAAK,YAAY,EAAE;YAC1F,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,UAAiB,QAAQ,cAAc,GAAG,IAAI,MAAM;YAE9F,SAAS;gBACP,eAAe,SAAS,MAAM;gBAC9B,YAAY,MAAM,MAAM;gBACxB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,wIAAA,CAAA,UAAe;oBAAC,eAAe;;;;;;YACzC,KAAK;gBACH,qBAAO,6LAAC,qIAAA,CAAA,UAAY;oBAAC,eAAe;;;;;;YACtC;gBACE,qBAAO,6LAAC,uIAAA,CAAA,UAAc;oBAAC,OAAO;;;;;;QAClC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,gIAAA,CAAA,UAAO;gBAAC,eAAe;gBAAe,kBAAkB;;;;;;0BACzD,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCACX,kBAAkB,eAAe;wCACjC,kBAAkB,cAAc;wCAChC,kBAAkB,WAAW;;;;;;;8CAEhC,6LAAC;oCAAE,WAAU;;wCACV,kBAAkB,eAAe;wCACjC,kBAAkB,cAAc;wCAChC,kBAAkB,WAAW;;;;;;;;;;;;;wBAGjC;;;;;;;;;;;;;;;;;;AAKX;GAzEwB;KAAA", "debugId": null}}]}