(()=>{var e={};e.id=856,e.ids=[856],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});let s=(0,r(66437).UU)("your_supabase_project_url","your_supabase_anon_key")},57907:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>x,serverHooks:()=>q,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>d,GET:()=>p,PUT:()=>c});var o=r(96559),u=r(48088),i=r(37719),a=r(32190),n=r(56621);async function p(e,{params:t}){try{let{id:e}=await t,{data:r,error:s}=await n.N.from("products").select("*").eq("id",e).single();if(s)return a.NextResponse.json({error:s.message},{status:500});if(!r)return a.NextResponse.json({error:"Product not found"},{status:404});return a.NextResponse.json({product:r})}catch{return a.NextResponse.json({error:"Failed to fetch product"},{status:500})}}async function c(e,{params:t}){try{let{id:r}=await t,{name:s,image_url:o,net_weight:u,price:i,stock_quantity:p,category:c}=await e.json();if(!s||!u||!i||!c)return a.NextResponse.json({error:"Missing required fields"},{status:400});let{data:d,error:x}=await n.N.from("products").update({name:s,image_url:o,net_weight:u,price:parseFloat(i),stock_quantity:parseInt(p)||0,category:c}).eq("id",r).select().single();if(x)return a.NextResponse.json({error:x.message},{status:500});return a.NextResponse.json({product:d})}catch{return a.NextResponse.json({error:"Failed to update product"},{status:500})}}async function d(e,{params:t}){try{let{id:e}=await t,{error:r}=await n.N.from("products").delete().eq("id",e);if(r)return a.NextResponse.json({error:r.message},{status:500});return a.NextResponse.json({message:"Product deleted successfully"})}catch{return a.NextResponse.json({error:"Failed to delete product"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/products/[id]/route",pathname:"/api/products/[id]",filename:"route",bundlePath:"app/api/products/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\products\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:f,serverHooks:q}=x;function j(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:f})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,437],()=>r(57907));module.exports=s})();