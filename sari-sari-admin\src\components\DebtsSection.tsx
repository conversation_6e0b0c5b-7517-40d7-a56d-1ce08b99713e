'use client'

import { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'
import DebtModal from './DebtModal'
import { CustomerDebt } from '@/lib/supabase'
import { format } from 'date-fns'

interface DebtsSectionProps {
  onStatsUpdate: () => void
}

export default function DebtsSection({ onStatsUpdate }: DebtsSectionProps) {
  const [debts, setDebts] = useState<CustomerDebt[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null)

  useEffect(() => {
    fetchDebts()
  }, [])

  const fetchDebts = async () => {
    try {
      const response = await fetch('/api/debts')
      const data = await response.json()
      setDebts(data.debts || [])
    } catch (error) {
      console.error('Error fetching debts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this debt record?')) return

    try {
      const response = await fetch(`/api/debts/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setDebts(debts.filter(d => d.id !== id))
        onStatsUpdate()
      }
    } catch (error) {
      console.error('Error deleting debt:', error)
    }
  }

  const handleEdit = (debt: CustomerDebt) => {
    setEditingDebt(debt)
    setIsModalOpen(true)
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
    setEditingDebt(null)
    fetchDebts()
    onStatsUpdate()
  }

  const filteredDebts = debts.filter(debt => {
    const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()
    const productName = debt.product_name.toLowerCase()
    const search = searchTerm.toLowerCase()
    return customerName.includes(search) || productName.includes(search)
  })

  // Group debts by customer
  const groupedDebts = filteredDebts.reduce((acc, debt) => {
    const customerKey = `${debt.customer_name} ${debt.customer_family_name}`
    if (!acc[customerKey]) {
      acc[customerKey] = []
    }
    acc[customerKey].push(debt)
    return acc
  }, {} as Record<string, CustomerDebt[]>)

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search by customer or product..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button
          onClick={() => setIsModalOpen(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Debt Record
        </button>
      </div>

      {/* Debts List */}
      <div className="space-y-6">
        {Object.entries(groupedDebts).map(([customerName, customerDebts]) => {
          const totalAmount = customerDebts.reduce((sum, debt) => sum + debt.total_amount, 0)
          
          return (
            <div key={customerName} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900">{customerName}</h3>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">{customerDebts.length} item(s)</p>
                    <p className="text-lg font-bold text-red-600">₱{totalAmount.toFixed(2)}</p>
                  </div>
                </div>
              </div>
              
              <div className="divide-y divide-gray-200">
                {customerDebts.map((debt) => (
                  <div key={debt.id} className="px-6 py-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{debt.product_name}</h4>
                        <div className="mt-1 text-sm text-gray-600 space-y-1">
                          <div className="flex items-center">
                            <span>Quantity: {debt.quantity}</span>
                            <span className="mx-2">•</span>
                            <span>Unit Price: ₱{debt.product_price.toFixed(2)}</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            <span>Date: {format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">₱{debt.total_amount.toFixed(2)}</p>
                        </div>
                        <button
                          onClick={() => handleEdit(debt)}
                          className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(debt.id)}
                          className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        })}
      </div>

      {filteredDebts.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No debt records found</h3>
          <p className="text-gray-600">
            {searchTerm
              ? 'Try adjusting your search criteria'
              : 'Get started by adding your first debt record'}
          </p>
        </div>
      )}

      {/* Debt Modal */}
      <DebtModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        debt={editingDebt}
      />
    </div>
  )
}
