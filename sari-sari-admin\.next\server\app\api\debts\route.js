(()=>{var e={};e.id=168,e.ids=[168],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17047:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>c});var o=r(96559),i=r(48088),u=r(37719),a=r(32190),n=r(56621);async function p(){try{let{data:e,error:t}=await n.N.from("customer_debts").select("*").order("created_at",{ascending:!1});if(t)return a.NextResponse.json({error:t.message},{status:500});return a.NextResponse.json({debts:e})}catch{return a.NextResponse.json({error:"Failed to fetch customer debts"},{status:500})}}async function c(e){try{let{customer_name:t,customer_family_name:r,product_name:s,product_price:o,quantity:i,debt_date:u}=await e.json();if(!t||!r||!s||!o||!i)return a.NextResponse.json({error:"Missing required fields"},{status:400});let{data:p,error:c}=await n.N.from("customer_debts").insert([{customer_name:t,customer_family_name:r,product_name:s,product_price:parseFloat(o),quantity:parseInt(i),debt_date:u||new Date().toISOString().split("T")[0]}]).select().single();if(c)return a.NextResponse.json({error:c.message},{status:500});return a.NextResponse.json({debt:p},{status:201})}catch{return a.NextResponse.json({error:"Failed to create customer debt"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/debts/route",pathname:"/api/debts",filename:"route",bundlePath:"app/api/debts/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\debts\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:m}=d;function q(){return(0,u.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});let s=(0,r(66437).UU)("your_supabase_project_url","your_supabase_anon_key")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,437],()=>r(17047));module.exports=s})();