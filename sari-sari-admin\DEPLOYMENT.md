# Deployment Guide - Sari-Sari Store Admin Dashboard

## 🚀 Ready to Deploy Your Dashboard?

This guide will help you deploy your Sari-Sari store admin dashboard to production.

## Recommended Deployment Platform: Vercel

Vercel is the easiest way to deploy Next.js applications and offers:
- ✅ Free tier available
- ✅ Automatic deployments from Git
- ✅ Built-in SSL certificates
- ✅ Global CDN
- ✅ Environment variable management

## Step-by-Step Deployment

### 1. Prepare Your Code

```bash
# Make sure everything is committed to Git
git add .
git commit -m "Ready for deployment"
git push origin main
```

### 2. Deploy to Vercel

**Option A: Using Vercel CLI (Recommended)**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy from your project directory
cd sari-sari-admin
vercel

# Follow the prompts:
# - Link to existing project? No
# - Project name: sari-sari-admin
# - Directory: ./
# - Override settings? No
```

**Option B: Using Vercel Dashboard**
1. Go to [vercel.com](https://vercel.com)
2. Sign up/login with GitHub
3. Click "New Project"
4. Import your repository
5. Configure project settings
6. Deploy

### 3. Set Environment Variables

In your Vercel dashboard:
1. Go to your project settings
2. Click "Environment Variables"
3. Add all variables from your `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
NEXTAUTH_SECRET=your_production_secret
NEXTAUTH_URL=https://your-app.vercel.app
```

**Important**: Update `NEXTAUTH_URL` to your actual Vercel domain!

### 4. Configure Custom Domain (Optional)

1. In Vercel dashboard, go to "Domains"
2. Add your custom domain (e.g., admin.yoursaristore.com)
3. Update DNS records as instructed
4. Update `NEXTAUTH_URL` to your custom domain

## Alternative Deployment Options

### Netlify
1. Connect your GitHub repository
2. Build command: `npm run build`
3. Publish directory: `.next`
4. Add environment variables in site settings

### Railway
1. Connect GitHub repository
2. Add environment variables
3. Deploy automatically

### DigitalOcean App Platform
1. Create new app from GitHub
2. Configure build settings
3. Add environment variables
4. Deploy

## Production Checklist

### Security
- ✅ Use strong, unique `NEXTAUTH_SECRET`
- ✅ Enable Row Level Security in Supabase
- ✅ Review Supabase API keys and permissions
- ✅ Use HTTPS only (automatic with Vercel)

### Performance
- ✅ Enable Cloudinary auto-optimization
- ✅ Configure image compression settings
- ✅ Set up proper caching headers

### Monitoring
- ✅ Set up Vercel Analytics
- ✅ Monitor Supabase usage
- ✅ Track Cloudinary bandwidth usage

## Database Production Setup

### Supabase Production Tips
1. **Upgrade Plan**: Consider upgrading from free tier for production
2. **Backups**: Enable automatic backups
3. **Security**: Review and tighten RLS policies
4. **Monitoring**: Set up alerts for usage limits

### Row Level Security (RLS) Setup
```sql
-- Enable RLS on tables
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_debts ENABLE ROW LEVEL SECURITY;

-- Create policies (example - adjust based on your auth needs)
CREATE POLICY "Enable all operations for authenticated users" ON products
FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON customer_debts
FOR ALL USING (auth.role() = 'authenticated');
```

## Post-Deployment Testing

### Test All Features
1. ✅ Dashboard loads correctly
2. ✅ Product CRUD operations work
3. ✅ Image uploads to Cloudinary work
4. ✅ Customer debt CRUD operations work
5. ✅ Search and filtering work
6. ✅ Responsive design on mobile
7. ✅ All API endpoints respond correctly

### Performance Testing
- Test loading times
- Check image optimization
- Verify mobile responsiveness
- Test with sample data

## Maintenance

### Regular Tasks
- Monitor Supabase database size
- Check Cloudinary usage
- Review application logs
- Update dependencies monthly
- Backup important data

### Scaling Considerations
- **Database**: Upgrade Supabase plan as needed
- **Images**: Monitor Cloudinary bandwidth
- **Hosting**: Vercel scales automatically
- **Users**: Consider adding authentication for multiple users

## Troubleshooting Production Issues

### Common Problems
1. **Environment Variables**: Double-check all are set correctly
2. **CORS Issues**: Ensure Supabase allows your domain
3. **Image Upload Fails**: Verify Cloudinary settings
4. **Database Connection**: Check Supabase status

### Debugging Tools
- Vercel Function Logs
- Browser Developer Tools
- Supabase Dashboard Logs
- Cloudinary Media Library

## Cost Estimation

### Free Tier Limits
- **Vercel**: 100GB bandwidth, 100 deployments/month
- **Supabase**: 500MB database, 2GB bandwidth
- **Cloudinary**: 25 credits/month (≈25,000 images)

### Paid Plans (if needed)
- **Vercel Pro**: $20/month per user
- **Supabase Pro**: $25/month per project
- **Cloudinary**: $89/month for advanced features

## Support

### Getting Help
- Vercel: [vercel.com/support](https://vercel.com/support)
- Supabase: [supabase.com/docs](https://supabase.com/docs)
- Cloudinary: [cloudinary.com/documentation](https://cloudinary.com/documentation)

Your Sari-Sari Store Admin Dashboard is now ready for production! 🎉

Remember to test thoroughly before going live and keep your environment variables secure.
