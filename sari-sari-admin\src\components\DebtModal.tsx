'use client'

import { useState, useEffect } from 'react'
import { X } from 'lucide-react'
import { CustomerDebt } from '@/lib/supabase'

interface DebtModalProps {
  isOpen: boolean
  onClose: () => void
  debt?: CustomerDebt | null
}

export default function DebtModal({ isOpen, onClose, debt }: DebtModalProps) {
  const [formData, setFormData] = useState({
    customer_name: '',
    customer_family_name: '',
    product_name: '',
    product_price: '',
    quantity: '',
    debt_date: ''
  })
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (debt) {
      setFormData({
        customer_name: debt.customer_name,
        customer_family_name: debt.customer_family_name,
        product_name: debt.product_name,
        product_price: debt.product_price.toString(),
        quantity: debt.quantity.toString(),
        debt_date: debt.debt_date
      })
    } else {
      setFormData({
        customer_name: '',
        customer_family_name: '',
        product_name: '',
        product_price: '',
        quantity: '',
        debt_date: new Date().toISOString().split('T')[0]
      })
    }
  }, [debt, isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const debtData = {
        ...formData,
        product_price: parseFloat(formData.product_price),
        quantity: parseInt(formData.quantity)
      }

      const url = debt ? `/api/debts/${debt.id}` : '/api/debts'
      const method = debt ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(debtData),
      })

      if (response.ok) {
        onClose()
      } else {
        console.error('Error saving debt record')
      }
    } catch (error) {
      console.error('Error saving debt record:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  const totalAmount = formData.product_price && formData.quantity 
    ? (parseFloat(formData.product_price) * parseInt(formData.quantity)).toFixed(2)
    : '0.00'

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {debt ? 'Edit Debt Record' : 'Add New Debt Record'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Customer Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer First Name *
            </label>
            <input
              type="text"
              required
              value={formData.customer_name}
              onChange={(e) => setFormData({ ...formData, customer_name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Juan"
            />
          </div>

          {/* Customer Family Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer Family Name *
            </label>
            <input
              type="text"
              required
              value={formData.customer_family_name}
              onChange={(e) => setFormData({ ...formData, customer_family_name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Dela Cruz"
            />
          </div>

          {/* Product Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Name *
            </label>
            <input
              type="text"
              required
              value={formData.product_name}
              onChange={(e) => setFormData({ ...formData, product_name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Lucky Me Pancit Canton"
            />
          </div>

          {/* Product Price */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Price (₱) *
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              required
              value={formData.product_price}
              onChange={(e) => setFormData({ ...formData, product_price: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="0.00"
            />
          </div>

          {/* Quantity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Quantity *
            </label>
            <input
              type="number"
              min="1"
              required
              value={formData.quantity}
              onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="1"
            />
          </div>

          {/* Debt Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Debt Date *
            </label>
            <input
              type="date"
              required
              value={formData.debt_date}
              onChange={(e) => setFormData({ ...formData, debt_date: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Total Amount Display */}
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">Total Amount:</span>
              <span className="text-lg font-bold text-green-600">₱{totalAmount}</span>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Saving...' : (debt ? 'Update' : 'Add Record')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
