'use client'

import { motion } from 'framer-motion'
import { ArrowRight, ShoppingBag, Users, BarChart3, Shield, Star, CheckCircle } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

export default function LandingPage() {
  const features = [
    {
      icon: ShoppingBag,
      title: 'Product Lists Management',
      description: 'Complete CRUD operations for your product inventory with image uploads and categorization.',
    },
    {
      icon: Users,
      title: 'Customer Debt Tracking',
      description: 'Efficiently manage customer credit (utang) with detailed records and payment tracking.',
    },
    {
      icon: BarChart3,
      title: 'Business Analytics',
      description: 'Visual charts and reports to understand your business performance and trends.',
    },
    {
      icon: Shield,
      title: 'Secure & Reliable',
      description: 'Built with modern security practices and reliable cloud infrastructure.',
    },
  ]

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Store Owner',
      content: 'Revantad Store has transformed how I manage my sari-sari store. The debt tracking feature is a game-changer!',
      rating: 5,
    },
    {
      name: '<PERSON>',
      role: 'Business Owner',
      content: 'The product management system is so easy to use. I can update my inventory anywhere, anytime.',
      rating: 5,
    },
    {
      name: '<PERSON>',
      role: 'Entrepreneur',
      content: 'Professional, reliable, and perfect for small businesses. Highly recommended!',
      rating: 5,
    },
  ]

  return (
    <div className="min-h-screen bg-white dark:bg-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center space-x-2"
            >
              <div className="w-8 h-8 hero-gradient rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">R</span>
              </div>
              <span className="text-xl font-bold text-gradient">Revantad Store</span>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Link href="/admin" className="btn-primary">
                Go to Admin
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </motion.div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 pb-16 hero-gradient">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-white"
            >
              <h1 className="text-5xl lg:text-6xl font-bold font-display mb-6 leading-tight">
                Modernize Your
                <span className="block text-yellow-300">Sari-Sari Store</span>
              </h1>
              <p className="text-xl mb-8 text-green-100 leading-relaxed">
                Professional admin dashboard for managing products, customer debt, and business analytics. 
                Built specifically for Filipino entrepreneurs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/admin" className="btn-secondary inline-flex items-center justify-center">
                  Start Managing
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
                <button className="btn-outline bg-white/10 border-white/30 text-white hover:bg-white hover:text-green-600">
                  Watch Demo
                </button>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="relative z-10 bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-white/20 rounded-lg">
                    <span className="text-white font-medium">Total Products</span>
                    <span className="text-yellow-300 font-bold text-2xl">247</span>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-white/20 rounded-lg">
                    <span className="text-white font-medium">Customer Debts</span>
                    <span className="text-yellow-300 font-bold text-2xl">₱12,450</span>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-white/20 rounded-lg">
                    <span className="text-white font-medium">Monthly Revenue</span>
                    <span className="text-yellow-300 font-bold text-2xl">₱45,200</span>
                  </div>
                </div>
              </div>
              <div className="absolute -top-4 -right-4 w-72 h-72 bg-yellow-400/20 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-4 -left-4 w-72 h-72 bg-green-400/20 rounded-full blur-3xl"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Everything You Need to <span className="text-gradient">Succeed</span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Comprehensive tools designed specifically for Filipino sari-sari store owners
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card p-6 text-center hover:shadow-lg transition-all duration-300"
              >
                <div className="w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Trusted by <span className="text-gradient">Store Owners</span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              See what our users say about Revantad Store
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card p-6"
              >
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-4 italic">
                  "{testimonial.content}"
                </p>
                <div>
                  <p className="font-semibold text-gray-900 dark:text-white">
                    {testimonial.name}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {testimonial.role}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 hero-gradient">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-white"
          >
            <h2 className="text-4xl font-bold mb-6">
              Ready to Transform Your Business?
            </h2>
            <p className="text-xl mb-8 text-green-100">
              Join hundreds of store owners who have modernized their operations with Revantad Store
            </p>
            <Link href="/admin" className="btn-secondary inline-flex items-center text-lg px-8 py-4">
              Get Started Today
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-8 h-8 hero-gradient rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">R</span>
              </div>
              <span className="text-xl font-bold text-gradient">Revantad Store</span>
            </div>
            <p className="text-gray-400 mb-4">
              Professional admin dashboard for modern sari-sari stores
            </p>
            <p className="text-gray-500 text-sm">
              © 2024 Revantad Store. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
