# Quick Setup Guide for Sari-Sari Store Admin Dashboard

## 🚀 Your application is now running at http://localhost:3000

## Next Steps to Complete Setup

### 1. Set up Supabase Database (Required)

**Create a Supabase Project:**
1. Go to [supabase.com](https://supabase.com) and create a free account
2. Create a new project
3. Wait for the project to be ready (2-3 minutes)

**Get your credentials:**
1. Go to Settings > API
2. Copy your Project URL and anon public key

**Set up the database:**
1. Go to SQL Editor in your Supabase dashboard
2. Copy and paste the contents of `database/schema.sql`
3. Click "Run" to create the tables and sample data

### 2. Set up Cloudinary (Required for image uploads)

**Create a Cloudinary Account:**
1. Go to [cloudinary.com](https://cloudinary.com) and sign up
2. Go to your Dashboard
3. Note your Cloud Name, API Key, and API Secret

**Create Upload Preset:**
1. Go to Settings > Upload
2. Click "Add upload preset"
3. Set Preset name: `sari-sari-products`
4. Set Signing Mode: "Unsigned"
5. Save

### 3. Update Environment Variables

Edit the `.env.local` file with your actual credentials:

```env
# Replace with your actual Supabase credentials
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Replace with your actual Cloudinary credentials
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Generate a random secret for NextAuth
NEXTAUTH_SECRET=your_random_secret_here
NEXTAUTH_URL=http://localhost:3000
```

### 4. Restart the Development Server

After updating the environment variables:
1. Stop the current server (Ctrl+C in terminal)
2. Run `npm run dev` again
3. The application will now be fully functional

## 🎉 Features You Can Test

### Dashboard
- View statistics overview
- See total products, debts, and amounts

### Product Lists (CRUD Operations)
- ✅ Create new product entries with images
- ✅ Read/view all products in organized lists
- ✅ Update existing product information
- ✅ Delete products from lists
- ✅ Search and filter by category
- ✅ View low stock alerts

### Customer Debt Management
- ✅ Add new debt records
- ✅ Edit existing debts
- ✅ Delete debt records
- ✅ View debts grouped by customer
- ✅ Search by customer or product name

## 📱 Responsive Design

The dashboard works perfectly on:
- Desktop computers
- Tablets
- Mobile phones

## 🔧 Troubleshooting

**If you see errors:**
1. Make sure Supabase credentials are correct
2. Ensure the database schema has been run
3. Check that Cloudinary credentials are valid
4. Restart the development server after changing .env.local

**Common Issues:**
- **"Failed to fetch"**: Check Supabase URL and keys
- **Image upload fails**: Verify Cloudinary setup and upload preset
- **Database errors**: Ensure schema.sql has been executed

## 📊 Sample Data

The database schema includes sample data:
- 8 sample products across different categories
- 5 sample customer debt records
- This helps you test the application immediately

## 🚀 Production Deployment

When ready to deploy:
1. Update NEXTAUTH_URL to your production domain
2. Deploy to Vercel, Netlify, or your preferred platform
3. Update environment variables in your hosting platform
4. Your Sari-Sari store admin dashboard is ready!

## 💡 Tips for Use

1. **Product Categories**: Use the predefined categories for consistency
2. **Stock Management**: Products with less than 10 items show "Low Stock" alerts
3. **Customer Debts**: Records are automatically grouped by customer name
4. **Search**: Use the search functionality to quickly find products or customers
5. **Images**: Upload clear product images for better inventory management

## 📞 Need Help?

If you encounter any issues:
1. Check the browser console for error messages
2. Verify all environment variables are set correctly
3. Ensure Supabase and Cloudinary services are working
4. Restart the development server

Your Sari-Sari Store Admin Dashboard is now ready to help you manage your business efficiently! 🏪
