import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// GET - Fetch single customer debt
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { data: debt, error } = await supabase
      .from('customer_debts')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!debt) {
      return NextResponse.json({ error: 'Debt record not found' }, { status: 404 })
    }

    return NextResponse.json({ debt })
  } catch {
    return NextResponse.json(
      { error: 'Failed to fetch debt record' },
      { status: 500 }
    )
  }
}

// PUT - Update customer debt
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      product_name,
      product_price,
      quantity,
      debt_date,
    } = body

    // Validate required fields
    if (
      !customer_name ||
      !customer_family_name ||
      !product_name ||
      !product_price ||
      !quantity
    ) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const { data: debt, error } = await supabase
      .from('customer_debts')
      .update({
        customer_name,
        customer_family_name,
        product_name,
        product_price: parseFloat(product_price),
        quantity: parseInt(quantity),
        debt_date: debt_date || new Date().toISOString().split('T')[0],
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ debt })
  } catch {
    return NextResponse.json(
      { error: 'Failed to update debt record' },
      { status: 500 }
    )
  }
}

// DELETE - Delete customer debt
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { error } = await supabase
      .from('customer_debts')
      .delete()
      .eq('id', id)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ message: 'Debt record deleted successfully' })
  } catch {
    return NextResponse.json(
      { error: 'Failed to delete debt record' },
      { status: 500 }
    )
  }
}
