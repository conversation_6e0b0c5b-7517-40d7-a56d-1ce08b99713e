[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\debts\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\debts\\[id]\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\products\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\products\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\upload\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\DashboardStats.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\DebtModal.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\DebtsSection.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\ProductModal.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\ProductsSection.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\Sidebar.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\lib\\cloudinary.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\lib\\supabase.ts": "15"}, {"size": 1837, "mtime": 1751939415459, "results": "16", "hashOfConfig": "17"}, {"size": 2672, "mtime": 1751939708403, "results": "18", "hashOfConfig": "17"}, {"size": 1592, "mtime": 1751939303617, "results": "19", "hashOfConfig": "17"}, {"size": 2410, "mtime": 1751939843383, "results": "20", "hashOfConfig": "17"}, {"size": 1415, "mtime": 1751939500686, "results": "21", "hashOfConfig": "17"}, {"size": 584, "mtime": 1751936455628, "results": "22", "hashOfConfig": "17"}, {"size": 2686, "mtime": 1751939262065, "results": "23", "hashOfConfig": "17"}, {"size": 4475, "mtime": 1751938272954, "results": "24", "hashOfConfig": "17"}, {"size": 7433, "mtime": 1751936715612, "results": "25", "hashOfConfig": "17"}, {"size": 7261, "mtime": 1751936687001, "results": "26", "hashOfConfig": "17"}, {"size": 8850, "mtime": 1751938233472, "results": "27", "hashOfConfig": "17"}, {"size": 6798, "mtime": 1751938207182, "results": "28", "hashOfConfig": "17"}, {"size": 1924, "mtime": 1751938159613, "results": "29", "hashOfConfig": "17"}, {"size": 967, "mtime": 1751938547415, "results": "30", "hashOfConfig": "17"}, {"size": 1026, "mtime": 1751938503960, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8hthbf", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\debts\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\debts\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\products\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\products\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\DashboardStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\DebtModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\DebtsSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\ProductModal.tsx", ["77"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\ProductsSection.tsx", ["78"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\components\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\lib\\cloudinary.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\sari-sari-admin\\src\\lib\\supabase.ts", [], [], {"ruleId": "79", "severity": 1, "message": "80", "line": 151, "column": 19, "nodeType": "81", "endLine": 155, "endColumn": 21}, {"ruleId": "79", "severity": 1, "message": "80", "line": 120, "column": 17, "nodeType": "81", "endLine": 124, "endColumn": 19}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]